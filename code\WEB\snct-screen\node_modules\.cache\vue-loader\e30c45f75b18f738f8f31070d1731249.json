{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue", "mtime": 1754041503773}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIOenu+mZpOS4jeWGjeS9v+eUqOeahOWvvOWFpQoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCaWdNYXAnLAogIHByb3BzOiB7CiAgICAvLyDlnLDlm77lrrnlmahJRAogICAgbWFwSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnYmlnZW1hcC1jb250YWluZXInCiAgICB9LAogICAgLy8g5Zyw5Zu+5a695bqmCiAgICB3aWR0aDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcxMDAlJwogICAgfSwKICAgIC8vIOWcsOWbvumrmOW6pgogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzEwMCUnCiAgICB9LAogICAgLy8g5Yid5aeL5Lit5b+D54K5CiAgICBjZW50ZXI6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFsxMjAuMCwgMzAuMF0KICAgIH0sCiAgICAvLyDliJ3lp4vnvKnmlL7nuqfliKsKICAgIHpvb206IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiA3CiAgICB9LAogICAgLy8g5piv5ZCm5pi+56S65o6n5Yi25oyJ6ZKuCiAgICBzaG93Q29udHJvbHM6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfSwKICAgIC8vIOWcsOWbvumFjee9rumAiemhuQogICAgb3B0aW9uczogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBtYXA6IG51bGwsCiAgICAgIGN1cnJlbnRMYXllcjogJ3NhdGVsbGl0ZScsCiAgICAgIHNob3dMYXllclBhbmVsOiBmYWxzZSwKICAgICAgYXZhaWxhYmxlTGF5ZXJzOiBbCiAgICAgICAgeyBrZXk6ICdzdHJlZXQnLCBuYW1lOiAn6KGX6YGT5Zu+JyB9LAogICAgICAgIHsga2V5OiAnc2F0ZWxsaXRlJywgbmFtZTogJ+WNq+aYn+WbvicgfSwKICAgICAgICB7IGtleTogJ3NlYScsIG5hbWU6ICfmtbflm74nIH0KICAgICAgXSwKICAgICAgaXNGdWxsc2NyZWVuOiBmYWxzZQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdE1hcCgpCiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKHRoaXMubWFwKSB7CiAgICAgIHRoaXMubWFwLnJlbW92ZSgpCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJblnLDlm74KICAgIGFzeW5jIGluaXRNYXAoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g562J5b6FIGJpZ2VtYXAg5bqT5Yqg6L295a6M5oiQCiAgICAgICAgYXdhaXQgdGhpcy53YWl0Rm9yQmlnZW1hcCgpCgogICAgICAgIC8vIOiuvue9ruWcsOWbvuacjeWKoeWZqOWcsOWdgCAtIOS9v+eUqOWumOaWueacjeWKoeWZqAogICAgICAgIEJNLkNvbmZpZy5IVFRQX1VSTCA9ICJodHRwOi8vd3d3LmJpZ2VtYXAuY29tOjkwMDAiCgogICAgICAgIC8vIOWIm+W7uuWcsOWbvuWunuS+iyAtIOWPguiAg+WumOaWueahiOS+iwogICAgICAgIHRoaXMubWFwID0gQk0ubWFwKHRoaXMubWFwSWQsICJiaWdlbWFwLmdvb2dsZW1hcC1zdHJlZXRzIiwgewogICAgICAgICAgY2VudGVyOiBbdGhpcy5jZW50ZXJbMF0sIHRoaXMuY2VudGVyWzFdXSwKICAgICAgICAgIHpvb206IHRoaXMuem9vbSwKICAgICAgICAgIHpvb21Db250cm9sOiB0cnVlLAogICAgICAgICAgLi4udGhpcy5vcHRpb25zCiAgICAgICAgfSkKCiAgICAgICAgLy8g57uR5a6a5Zyw5Zu+5LqL5Lu2CiAgICAgICAgdGhpcy5iaW5kTWFwRXZlbnRzKCkKCiAgICAgICAgLy8g6Kem5Y+R5Zyw5Zu+5Yid5aeL5YyW5a6M5oiQ5LqL5Lu2CiAgICAgICAgdGhpcy4kZW1pdCgnbWFwLXJlYWR5JywgdGhpcy5tYXApCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WcsOWbvuWIneWni+WMluWksei0pTonLCBlcnJvcikKICAgICAgICB0aGlzLiRlbWl0KCdtYXAtZXJyb3InLCBlcnJvcikKICAgICAgfQogICAgfSwKCiAgICAvLyDnrYnlvoUgYmlnZW1hcCDlupPliqDovb0KICAgIHdhaXRGb3JCaWdlbWFwKCkgewogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gewogICAgICAgIGlmICh3aW5kb3cuQk0pIHsKICAgICAgICAgIHJlc29sdmUoKQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICBsZXQgYXR0ZW1wdHMgPSAwCiAgICAgICAgY29uc3QgbWF4QXR0ZW1wdHMgPSA1MAogICAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICBhdHRlbXB0cysrCiAgICAgICAgICBpZiAod2luZG93LkJNKSB7CiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCkKICAgICAgICAgICAgcmVzb2x2ZSgpCiAgICAgICAgICB9IGVsc2UgaWYgKGF0dGVtcHRzID49IG1heEF0dGVtcHRzKSB7CiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCkKICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcignQmlnZW1hcCBsaWJyYXJ5IGZhaWxlZCB0byBsb2FkJykpCiAgICAgICAgICB9CiAgICAgICAgfSwgMTAwKQogICAgICB9KQogICAgfSwKCgoKICAgIC8vIOe7keWumuWcsOWbvuS6i+S7tgogICAgYmluZE1hcEV2ZW50cygpIHsKICAgICAgdGhpcy5tYXAub24oJ2NsaWNrJywgKGUpID0+IHsKICAgICAgICB0aGlzLiRlbWl0KCdtYXAtY2xpY2snLCBlKQogICAgICB9KQogICAgICAKICAgICAgdGhpcy5tYXAub24oJ3pvb20nLCAoZSkgPT4gewogICAgICAgIHRoaXMuJGVtaXQoJ21hcC16b29tJywgZSkKICAgICAgfSkKICAgICAgCiAgICAgIHRoaXMubWFwLm9uKCdtb3ZlZW5kJywgKGUpID0+IHsKICAgICAgICB0aGlzLiRlbWl0KCdtYXAtbW92ZWVuZCcsIGUpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWIh+aNouWbvuWxgumdouadv+aYvuekugogICAgdG9nZ2xlTGF5ZXIoKSB7CiAgICAgIHRoaXMuc2hvd0xheWVyUGFuZWwgPSAhdGhpcy5zaG93TGF5ZXJQYW5lbAogICAgfSwKCiAgICAvLyDliIfmjaLlnLDlm77lm77lsYIKICAgIHN3aXRjaExheWVyKGxheWVyS2V5KSB7CiAgICAgIGlmICh0aGlzLmN1cnJlbnRMYXllciA9PT0gbGF5ZXJLZXkpIHJldHVybgoKICAgICAgLy8g6YeN5paw5Yib5bu65Zyw5Zu+5a6e5L6L5Lul5YiH5o2i5Zu+5bGCCiAgICAgIGxldCBtYXBJZCA9ICcnCiAgICAgIGlmIChsYXllcktleSA9PT0gJ3NhdGVsbGl0ZScpIHsKICAgICAgICBtYXBJZCA9ICdiaWdlbWFwLmdvb2dsZW1hcC1zYXRlbGxpdGUnCiAgICAgIH0gZWxzZSBpZiAobGF5ZXJLZXkgPT09ICdzZWEnKSB7CiAgICAgICAgbWFwSWQgPSAnYmlnZW1hcC5zZWFNYXAnCiAgICAgIH0gZWxzZSBpZiAobGF5ZXJLZXkgPT09ICdzdHJlZXQnKSB7CiAgICAgICAgbWFwSWQgPSAnYmlnZW1hcC5nb29nbGVtYXAtc3RyZWV0cycKICAgICAgfQoKICAgICAgaWYgKG1hcElkKSB7CiAgICAgICAgLy8g5L+d5a2Y5b2T5YmN5Zyw5Zu+54q25oCBCiAgICAgICAgY29uc3QgY3VycmVudENlbnRlciA9IHRoaXMubWFwLmdldENlbnRlcigpCiAgICAgICAgY29uc3QgY3VycmVudFpvb20gPSB0aGlzLm1hcC5nZXRab29tKCkKCiAgICAgICAgLy8g56e76Zmk5pen5Zyw5Zu+CiAgICAgICAgdGhpcy5tYXAucmVtb3ZlKCkKCiAgICAgICAgLy8g5Yib5bu65paw5Zyw5Zu+CiAgICAgICAgdGhpcy5tYXAgPSBCTS5tYXAodGhpcy5tYXBJZCwgbWFwSWQsIHsKICAgICAgICAgIGNlbnRlcjogW2N1cnJlbnRDZW50ZXIubG5nLCBjdXJyZW50Q2VudGVyLmxhdF0sCiAgICAgICAgICB6b29tOiBjdXJyZW50Wm9vbSwKICAgICAgICAgIHpvb21Db250cm9sOiB0cnVlCiAgICAgICAgfSkKCiAgICAgICAgLy8g6YeN5paw57uR5a6a5LqL5Lu2CiAgICAgICAgdGhpcy5iaW5kTWFwRXZlbnRzKCkKICAgICAgfQoKICAgICAgdGhpcy5jdXJyZW50TGF5ZXIgPSBsYXllcktleQogICAgICB0aGlzLnNob3dMYXllclBhbmVsID0gZmFsc2UKICAgICAgdGhpcy4kZW1pdCgnbGF5ZXItY2hhbmdlZCcsIGxheWVyS2V5KQogICAgfSwKCiAgICAvLyDph43nva7op4blm74KICAgIHJlc2V0VmlldygpIHsKICAgICAgdGhpcy5tYXAuc2V0VmlldyhbdGhpcy5jZW50ZXJbMF0sIHRoaXMuY2VudGVyWzFdXSwgdGhpcy56b29tKQogICAgfSwKCiAgICAvLyDliIfmjaLlhajlsY8KICAgIHRvZ2dsZUZ1bGxzY3JlZW4oKSB7CiAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gIXRoaXMuaXNGdWxsc2NyZWVuCiAgICAgIHRoaXMuJGVtaXQoJ2Z1bGxzY3JlZW4tdG9nZ2xlJywgdGhpcy5pc0Z1bGxzY3JlZW4pCiAgICB9LAoKICAgIC8vIOiOt+WPluWcsOWbvuWunuS+iwogICAgZ2V0TWFwKCkgewogICAgICByZXR1cm4gdGhpcy5tYXAKICAgIH0sCgogICAgLy8g6K6+572u5Zyw5Zu+5Lit5b+D54K5CiAgICBzZXRDZW50ZXIobG5nLCBsYXQsIHpvb20pIHsKICAgICAgaWYgKHRoaXMubWFwKSB7CiAgICAgICAgdGhpcy5tYXAuc2V0VmlldyhbbG5nLCBsYXRdLCB6b29tIHx8IHRoaXMubWFwLmdldFpvb20oKSkKICAgICAgfQogICAgfSwKCiAgICAvLyDmt7vliqDmoIforrAKICAgIGFkZE1hcmtlcihsbmcsIGxhdCwgb3B0aW9ucyA9IHt9KSB7CiAgICAgIGlmICghdGhpcy5tYXApIHJldHVybiBudWxsCgogICAgICBjb25zdCBtYXJrZXIgPSBCTS5tYXJrZXIoW2xuZywgbGF0XSwgb3B0aW9ucykKICAgICAgbWFya2VyLmFkZFRvKHRoaXMubWFwKQogICAgICByZXR1cm4gbWFya2VyCiAgICB9LAoKICAgIC8vIOenu+mZpOagh+iusAogICAgcmVtb3ZlTWFya2VyKG1hcmtlcikgewogICAgICBpZiAodGhpcy5tYXAgJiYgbWFya2VyKSB7CiAgICAgICAgdGhpcy5tYXAucmVtb3ZlTGF5ZXIobWFya2VyKQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["BigMap.vue"], "names": [], "mappings": ";AAmCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BigMap.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\n  <div class=\"big-map-container\">\n    <div \n      :id=\"mapId\" \n      class=\"map-wrapper\"\n      :style=\"{ width: width, height: height }\"\n    ></div>\n    \n    <!-- 地图控制按钮 -->\n    <div class=\"map-controls\" v-if=\"showControls\">\n      <div class=\"control-btn\" @click=\"toggleLayer\" title=\"切换图层\">\n        <i class=\"el-icon-picture\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"resetView\" title=\"重置视图\">\n        <i class=\"el-icon-refresh\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"toggleFullscreen\" title=\"全屏\">\n        <i class=\"el-icon-full-screen\"></i>\n      </div>\n    </div>\n\n    <!-- 图层切换面板 -->\n    <div class=\"layer-panel\" v-show=\"showLayerPanel\">\n      <div class=\"layer-item\" \n           v-for=\"layer in availableLayers\" \n           :key=\"layer.key\"\n           :class=\"{ active: currentLayer === layer.key }\"\n           @click=\"switchLayer(layer.key)\">\n        {{ layer.name }}\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n// 移除不再使用的导入\n\nexport default {\n  name: 'BigMap',\n  props: {\n    // 地图容器ID\n    mapId: {\n      type: String,\n      default: 'bigemap-container'\n    },\n    // 地图宽度\n    width: {\n      type: String,\n      default: '100%'\n    },\n    // 地图高度\n    height: {\n      type: String,\n      default: '100%'\n    },\n    // 初始中心点\n    center: {\n      type: Array,\n      default: () => [120.0, 30.0]\n    },\n    // 初始缩放级别\n    zoom: {\n      type: Number,\n      default: 7\n    },\n    // 是否显示控制按钮\n    showControls: {\n      type: Boolean,\n      default: true\n    },\n    // 地图配置选项\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      map: null,\n      currentLayer: 'satellite',\n      showLayerPanel: false,\n      availableLayers: [\n        { key: 'street', name: '街道图' },\n        { key: 'satellite', name: '卫星图' },\n        { key: 'sea', name: '海图' }\n      ],\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.initMap()\n  },\n  beforeDestroy() {\n    if (this.map) {\n      this.map.remove()\n    }\n  },\n  methods: {\n    // 初始化地图\n    async initMap() {\n      try {\n        // 等待 bigemap 库加载完成\n        await this.waitForBigemap()\n\n        // 设置地图服务器地址 - 使用官方服务器\n        BM.Config.HTTP_URL = \"http://www.bigemap.com:9000\"\n\n        // 创建地图实例 - 参考官方案例\n        this.map = BM.map(this.mapId, \"bigemap.googlemap-streets\", {\n          center: [this.center[0], this.center[1]],\n          zoom: this.zoom,\n          zoomControl: true,\n          ...this.options\n        })\n\n        // 绑定地图事件\n        this.bindMapEvents()\n\n        // 触发地图初始化完成事件\n        this.$emit('map-ready', this.map)\n\n      } catch (error) {\n        console.error('地图初始化失败:', error)\n        this.$emit('map-error', error)\n      }\n    },\n\n    // 等待 bigemap 库加载\n    waitForBigemap() {\n      return new Promise((resolve, reject) => {\n        if (window.BM) {\n          resolve()\n          return\n        }\n\n        let attempts = 0\n        const maxAttempts = 50\n        const checkInterval = setInterval(() => {\n          attempts++\n          if (window.BM) {\n            clearInterval(checkInterval)\n            resolve()\n          } else if (attempts >= maxAttempts) {\n            clearInterval(checkInterval)\n            reject(new Error('Bigemap library failed to load'))\n          }\n        }, 100)\n      })\n    },\n\n\n\n    // 绑定地图事件\n    bindMapEvents() {\n      this.map.on('click', (e) => {\n        this.$emit('map-click', e)\n      })\n      \n      this.map.on('zoom', (e) => {\n        this.$emit('map-zoom', e)\n      })\n      \n      this.map.on('moveend', (e) => {\n        this.$emit('map-moveend', e)\n      })\n    },\n\n    // 切换图层面板显示\n    toggleLayer() {\n      this.showLayerPanel = !this.showLayerPanel\n    },\n\n    // 切换地图图层\n    switchLayer(layerKey) {\n      if (this.currentLayer === layerKey) return\n\n      // 重新创建地图实例以切换图层\n      let mapId = ''\n      if (layerKey === 'satellite') {\n        mapId = 'bigemap.googlemap-satellite'\n      } else if (layerKey === 'sea') {\n        mapId = 'bigemap.seaMap'\n      } else if (layerKey === 'street') {\n        mapId = 'bigemap.googlemap-streets'\n      }\n\n      if (mapId) {\n        // 保存当前地图状态\n        const currentCenter = this.map.getCenter()\n        const currentZoom = this.map.getZoom()\n\n        // 移除旧地图\n        this.map.remove()\n\n        // 创建新地图\n        this.map = BM.map(this.mapId, mapId, {\n          center: [currentCenter.lng, currentCenter.lat],\n          zoom: currentZoom,\n          zoomControl: true\n        })\n\n        // 重新绑定事件\n        this.bindMapEvents()\n      }\n\n      this.currentLayer = layerKey\n      this.showLayerPanel = false\n      this.$emit('layer-changed', layerKey)\n    },\n\n    // 重置视图\n    resetView() {\n      this.map.setView([this.center[0], this.center[1]], this.zoom)\n    },\n\n    // 切换全屏\n    toggleFullscreen() {\n      this.isFullscreen = !this.isFullscreen\n      this.$emit('fullscreen-toggle', this.isFullscreen)\n    },\n\n    // 获取地图实例\n    getMap() {\n      return this.map\n    },\n\n    // 设置地图中心点\n    setCenter(lng, lat, zoom) {\n      if (this.map) {\n        this.map.setView([lng, lat], zoom || this.map.getZoom())\n      }\n    },\n\n    // 添加标记\n    addMarker(lng, lat, options = {}) {\n      if (!this.map) return null\n\n      const marker = BM.marker([lng, lat], options)\n      marker.addTo(this.map)\n      return marker\n    },\n\n    // 移除标记\n    removeMarker(marker) {\n      if (this.map && marker) {\n        this.map.removeLayer(marker)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.big-map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.map-wrapper {\n  border: 2px solid #0ec6c4;\n  border-radius: 4px;\n}\n\n.map-controls {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.control-btn {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.control-btn:hover {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.layer-panel {\n  position: absolute;\n  top: 10px;\n  right: 50px;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 8px;\n  min-width: 100px;\n}\n\n.layer-item {\n  padding: 6px 12px;\n  cursor: pointer;\n  border-radius: 3px;\n  transition: all 0.3s;\n}\n\n.layer-item:hover {\n  background: #f5f5f5;\n}\n\n.layer-item.active {\n  background: #409eff;\n  color: white;\n}\n</style>\n"]}]}